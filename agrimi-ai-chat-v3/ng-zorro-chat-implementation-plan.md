# ng-zorro-antd Chat Interface Implementation Plan

## Overview
This document outlines the implementation plan for recreating the Agrimi AI chat interface using ng-zorro-antd components. The design features a sidebar-content layout with conversation history, main chat area, and input controls.

## UI Design Analysis

### Figma Frame Analysis (Node ID: 15459:16516)

Based on detailed inspection of the Figma design, this is a chat interface frame for "Agrimi AI" with a vertical layout consisting of three main sections:

#### 1. Header Section (Top)
**Container**: Purple header bar with rounded top corners
- **Background**: Light purple (#ab85e8)
- **Padding**: 16px all around
- **Border radius**: 16px on top-left and top-right corners
- **Layout**: Horizontal flexbox with space-between alignment

**Left Side Elements**:
- **Icon Wrapper**: 32x32px container for an icon (currently empty/placeholder)
- **Title Text**: "Agrimi AI"
  - Font: Montserrat Bold, 16px, white color (#ffffff)
  - Line height: 24px

**Right Side Elements**:
- **Button 1**: 32x32px rounded button (8px border radius)
  - Contains an SVG icon image
  - Padding: 10px horizontal, 4px vertical
- **Button 2**: 32x32px rounded button
  - Contains the letter "A" in 14px Montserrat Medium font
  - Text color: rgba(0,0,0,0.85)
  - Has an overlaid icon wrapper

#### 2. Main Content Area (Middle)
**Container**: White background, flexible height that grows to fill available space

**Chat Message**:
- **Avatar**: 40x40px circular container (likely for user/AI avatar)
- **Message Bubble**:
  - Background: Light purple (#f9f0ff)
  - Padding: 16px
  - Border radius: 12px
  - Contains Bulgarian text: "Здравейте! Как мога да Ви помогна днес?" (Hello! How can I help you today?)
  - Font: Montserrat Medium, 14px
  - Text color: rgba(0,0,0,0.85)
  - Line height: 22px

#### 3. Input Section (Bottom)
**Container**: White background with vertical layout and 24px gap between elements

**Text Input Field**:
- **Background**: White (#ffffff)
- **Border**: 1px solid #d9d9d9
- **Border radius**: 12px
- **Padding**: 12px left/right, 12px top/bottom (16px left, 12px right specifically)
- **Shadow**: 0px 2px 8px rgba(0,0,0,0.15)
- **Layout**: Horizontal flexbox with space-between alignment
- **Placeholder text**: "Попитайте Agrimi AI" (Ask Agrimi AI)
  - Font: Montserrat Medium, 14px
  - Color: rgba(0,0,0,0.25)
- **Send button**: 32x32px icon on the right side

**Suggested Questions Section**:
- **Layout**: Horizontal flex wrap with 8px gap
- **Three suggestion buttons**, each with:
  - Background: Light purple (#f9f0ff)
  - Padding: 16px horizontal, 12px vertical
  - Border radius: 8px
  - Icon placeholder (24x24px) + text
  - Font: Montserrat Medium, 14px
  - Text color: rgba(0,0,0,0.85)

**Button texts** (in Bulgarian):
1. "Какво означават цветовете на картата?" (What do the colors on the map mean?)
2. "Как се сменя статус на договор?" (How to change contract status?)
3. "Как да филтрирам имоти по собственик или масив?" (How to filter properties by owner or array?)

#### Design System Elements

**Color Palette**:
- Primary purple: #ab85e8
- Light purple background: #f9f0ff
- White: #ffffff
- Text primary: rgba(0,0,0,0.85)
- Text secondary: rgba(0,0,0,0.45)
- Text disabled/placeholder: rgba(0,0,0,0.25)
- Border: #d9d9d9

**Typography**:
- Primary font family: Montserrat
- Header: Bold 16px (weight: 700, line-height: 24px)
- Body text: Medium 14px (weight: 500, line-height: 22px)

**Spacing System**:
- Consistent use of 8px, 12px, 16px, and 24px spacing
- Border radius: 8px for buttons, 12px for input fields, 16px for main containers

### Key Visual Elements Summary
1. **Main Layout**: Vertical chat interface with purple header
2. **Header**: Purple background (#ab85e8) with "Agrimi AI" branding and control buttons
3. **Main Content**: Chat area with AI greeting message in Bulgarian
4. **Input Area**: Text input with placeholder and send button
5. **Suggested Actions**: Three pill-shaped buttons with Bulgarian question prompts

## Component Mapping Strategy

### 1. Main Layout Structure

**Primary Container**: `nz-layout`
- **Header**: `nz-header` (purple header with branding)
- **Content Wrapper**: `nz-layout` (nested layout)
  - **Sidebar**: `nz-sider` (collapsible left navigation)
  - **Main Content**: `nz-content` (chat area)

```html
<nz-layout class="main-layout">
  <nz-header class="app-header">
    <!-- Header content -->
  </nz-header>
  <nz-layout class="content-wrapper">
    <nz-sider class="sidebar">
      <!-- Sidebar content -->
    </nz-sider>
    <nz-content class="main-content">
      <!-- Chat content -->
    </nz-content>
  </nz-layout>
</nz-layout>
```

### 2. Header Components

**Component**: `nz-header`
- **Configuration**: Custom purple background styling
- **Content**: 
  - Title/Branding: Simple text/icon combination
  - Window Controls: `nz-button` with `nzType="text"` and `nzShape="circle"`

**Properties**:
- Custom CSS class for purple background
- Flex layout for title and controls alignment

### 3. Sidebar Components

**Component**: `nz-sider`
- **Configuration**:
  - `nzWidth="280"`
  - `nzCollapsible="true"`
  - `nzTheme="light"`
  - `nzBreakpoint="lg"`

**Content Structure**:
1. **New Conversation Button**: `nz-button`
   - `nzType="default"`
   - `nzBlock="true"`
   - Plus icon prefix

2. **Conversation History**: `nz-menu`
   - `nzMode="inline"`
   - `nzTheme="light"`
   - Section headers with custom styling
   - `nz-menu-item` for each conversation

3. **Bottom Navigation**: `nz-menu`
   - `nz-menu-item` for settings, guides, contact
   - Icons with text labels

### 4. Main Chat Area Components

**Component**: `nz-content`

**Content Structure**:
1. **Welcome Section**:
   - Greeting text with custom typography
   - Subtitle with custom styling

2. **Suggested Questions**: `nz-flex`
   - `nzGap="middle"`
   - `nzWrap="wrap"`
   - `nzJustify="flex-start"`
   - `nzAlign="center"`
   - Question pills as `nz-button` with custom styling

3. **Input Area**: Fixed positioned container
   - `nz-input-group` with suffix button
   - `nz-input` for text input
   - `nz-button` for send action

### 5. Detailed Component Specifications

#### Sidebar Configuration
```typescript
// nz-sider properties
nzWidth = "280"
nzCollapsible = true
nzTheme = "light"
nzBreakpoint = "lg"
nzCollapsedWidth = 64
```

#### Menu Configuration
```typescript
// nz-menu properties
nzMode = "inline"
nzTheme = "light"
nzSelectable = true
```

#### Input Group Configuration
```typescript
// nz-input-group properties
nzSize = "large"
// nz-input properties
placeholder = "Попитайте Agrimi AI" // Bulgarian: "Ask Agrimi AI"
// Custom styling to match Figma specifications
style = {
  'border-radius': '12px',
  'padding': '12px 12px 12px 16px',
  'border': '1px solid #d9d9d9',
  'box-shadow': '0px 2px 8px rgba(0, 0, 0, 0.15)'
}
```

#### Button Configurations
```typescript
// New conversation button
nzType = "default"
nzBlock = true

// Suggested question buttons
nzType = "default"
// Custom CSS for pill shape

// Send button
nzType = "primary"
nzShape = "circle"
```

#### Flex Layout Configuration
```typescript
// Suggested questions container
nzJustify = "flex-start"
nzAlign = "center"
nzGap = "middle"
nzWrap = "wrap"
```

## Custom Styling Requirements

### CSS Classes Needed (Based on Figma Analysis)

1. **Header Styling**:
```css
.app-header {
  background-color: #ab85e8; /* Exact purple from Figma */
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-radius: 16px 16px 0 0; /* Rounded top corners */
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
}

.header-controls {
  display: flex;
  gap: 12px; /* 3 * 4px from Figma */
}

.header-control-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  padding: 4px 10px;
}
```

2. **Chat Message Bubble**:
```css
.chat-message-bubble {
  background-color: #f9f0ff; /* Light purple from Figma */
  padding: 16px;
  border-radius: 12px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
```

3. **Suggested Question Pills**:
```css
.question-pill {
  background-color: #f9f0ff; /* Light purple from Figma */
  border-radius: 8px; /* Exact radius from Figma */
  border: none;
  padding: 12px 16px; /* Exact padding from Figma */
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  gap: 8px; /* Gap between icon and text */
}

.question-pills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Exact gap from Figma */
}

.question-pill-icon {
  width: 24px;
  height: 24px;
}
```

4. **Input Area**:
```css
.input-area {
  background: #ffffff;
  padding: 24px 0; /* Vertical gap from Figma */
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-field {
  background: #ffffff;
  border: 1px solid #d9d9d9; /* Exact border color from Figma */
  border-radius: 12px; /* Exact radius from Figma */
  padding: 12px 12px 12px 16px; /* Exact padding from Figma */
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15); /* Exact shadow from Figma */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.input-placeholder {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.25); /* Exact placeholder color from Figma */
}

.send-button {
  width: 32px;
  height: 32px;
}
```

5. **Typography System**:
```css
.montserrat-bold-16 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
}

.montserrat-medium-14 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
}

.text-primary {
  color: rgba(0, 0, 0, 0.85);
}

.text-secondary {
  color: rgba(0, 0, 0, 0.45);
}

.text-disabled {
  color: rgba(0, 0, 0, 0.25);
}

.text-white {
  color: #ffffff;
}
```

6. **Color Variables**:
```css
:root {
  --primary-purple: #ab85e8;
  --light-purple: #f9f0ff;
  --white: #ffffff;
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.45);
  --text-disabled: rgba(0, 0, 0, 0.25);
  --border-color: #d9d9d9;
  --shadow-light: 0px 2px 8px rgba(0, 0, 0, 0.15);
}
```

## Icons Required

### Icon Mapping
- **Plus icon**: `plus` (new conversation)
- **Sparkle icons**: `star` or custom sparkle (suggested questions)
- **Send icon**: `send` or `arrow-right` (input submit)
- **Chat icons**: `message` (conversation history)
- **Settings icon**: `setting` (bottom menu)
- **Guide icon**: `question-circle` (bottom menu)
- **Contact icon**: `phone` (bottom menu)

### Icon Implementation
```html
<!-- Example icon usage -->
<nz-icon nzType="plus" nzTheme="outline"></nz-icon>
<nz-icon nzType="star" nzTheme="outline"></nz-icon>
<nz-icon nzType="send" nzTheme="outline"></nz-icon>
```

## Responsive Design Considerations

### Breakpoint Strategy
1. **Desktop (≥992px)**: Full sidebar visible
2. **Tablet (768px-991px)**: Collapsible sidebar
3. **Mobile (<768px)**: Hidden sidebar with overlay

### Implementation
```typescript
// Responsive sidebar configuration
@Component({
  template: `
    <nz-sider 
      [nzCollapsed]="isCollapsed"
      [nzBreakpoint]="'lg'"
      (nzCollapsedChange)="onCollapsedChange($event)">
    </nz-sider>
  `
})
```

### Mobile Adaptations
- Input area spans full width on mobile
- Sidebar becomes overlay drawer
- Suggested questions stack vertically
- Header adjusts for mobile viewport

## Implementation Priority

### Phase 1: Core Layout
1. Set up main `nz-layout` structure
2. Implement `nz-header` with basic styling
3. Configure `nz-sider` with basic menu
4. Set up `nz-content` area

### Phase 2: Sidebar Functionality
1. Implement conversation history with `nz-menu`
2. Add new conversation button
3. Create bottom navigation menu
4. Add hover and selection states

### Phase 3: Chat Interface
1. Create welcome message section
2. Implement suggested questions with `nz-flex`
3. Build input area with `nz-input-group`
4. Add send button functionality

### Phase 4: Styling & Polish
1. Apply custom CSS for brand colors
2. Implement pill-shaped buttons
3. Add responsive behaviors
4. Fine-tune spacing and typography

### Phase 5: Interactions
1. Add click handlers for menu items
2. Implement input submission
3. Add keyboard shortcuts
4. Handle responsive state changes

## Dependencies

### Required ng-zorro-antd Modules
```typescript
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFlexModule } from 'ng-zorro-antd/flex';
```

### Bulgarian Language Content

Based on the Figma analysis, the interface uses Bulgarian language content:

#### Text Content from Figma
```typescript
// Bulgarian text constants
export const BULGARIAN_CONTENT = {
  // Header
  appTitle: 'Agrimi AI',

  // Welcome message
  greeting: 'Здравейте!', // "Hello!"
  helpMessage: 'Как мога да Ви помогна днес?', // "How can I help you today?"

  // Input placeholder
  inputPlaceholder: 'Попитайте Agrimi AI', // "Ask Agrimi AI"

  // Suggested questions
  suggestedQuestions: [
    'Какво означават цветовете на картата?', // "What do the colors on the map mean?"
    'Как се сменя статус на договор?', // "How to change contract status?"
    'Как да филтрирам имоти по собственик или масив?' // "How to filter properties by owner or array?"
  ]
};
```

#### Internationalization Setup
```typescript
// For future i18n support
import { NzI18nModule, bg_BG } from 'ng-zorro-antd/i18n';

// Component configuration
export class ChatComponent {
  locale = bg_BG; // Bulgarian locale for ng-zorro components
}
```

### Font Requirements

The design specifically uses **Montserrat** font family:
- Import Montserrat from Google Fonts
- Weights needed: 500 (Medium), 700 (Bold)
- Ensure proper fallback to sans-serif

```css
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500;700&display=swap');
```

### Additional Considerations
- Ensure proper TypeScript types for component properties
- Implement proper accessibility attributes with Bulgarian language support
- Consider internationalization for text content (Bulgarian as primary, potential English support)
- Plan for theme customization capabilities
- Ensure proper RTL/LTR text direction support for Bulgarian content
- Test with Bulgarian character encoding (UTF-8)

This implementation plan provides a comprehensive roadmap for recreating the chat interface using ng-zorro-antd components while maintaining design fidelity, supporting Bulgarian language content, and ensuring responsive behavior.
